import datetime as dt
import logging
from typing import Sequence, cast
from uuid import UUID

from fastapi import UploadFile

from constants.extracted_data import ConfirmedDataFields, ConversationState, DataSourceType
from constants.message import (
    CLIENT_NAME_CONFIRMED,
    DATES_CONFIRMED,
    DATES_ONE_DATE,
    LDMF_COUNTRY_CONFIRMED,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    OptionType,
    SuggestedUserPrompt,
)
from exceptions import EntityNotFoundError
from repositories import ConversationMessageRepository, ConversationRepository
from schemas import (
    BaseMessageSerializer,
    ClientNameOption,
    CombinedMessageSerializer,
    ConversationMessageProcessingResult,
    DatePickerOption,
    DocumentCreationRequest,
    LDMFCountryOption,
    MessageValidator,
    Option,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from services.date_validator import DateValidatorService
from services.document import DocumentService
from services.extracted_data import ExtractedDataService
from services.intent_classifier import IntentClassifierService
from services.kx_dash import KXDashService
from services.message_processor import ConversationMessageProcessor
from services.suggestions import SuggestedPromptsGenerator


__all__ = ['ConversationMessageService']

logger = logging.getLogger(__name__)


class ConversationMessageService:
    """Service for conversation message-related business logic."""

    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        conversation_repository: ConversationRepository,
        document_service: DocumentService,
        kx_dash_service: KXDashService,
        intent_classifier_service: IntentClassifierService,
        extracted_data_service: ExtractedDataService,
        date_validator_service: DateValidatorService,
    ):
        self.conversation_message_repository = conversation_message_repository
        self.conversation_repository = conversation_repository
        self.document_service = document_service
        self.kx_dash_service = kx_dash_service
        self.intent_classifier_service = intent_classifier_service
        self.extracted_data_service = extracted_data_service
        self.date_validator_service = date_validator_service

    async def create(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        """
        Create a new conversation message with attached files.

        Args:
            conversation_id: UUID of the conversation
            content: Text content of the message
            selected_option: The selected option
            files: Optional list of files to attach

        Returns:
            Response containing both user message and system message with expected entity

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            MaximumDocumentsNumberExceeded: If too many documents are attached
            MaximumDocumentsSizeExceeded: If documents exceed size limit
            ValueError: If file validation fails
        """
        content = content.strip()
        if files:
            message_type = MessageType.TEXT_WITH_FILE if content else MessageType.FILE
        else:
            message_type = MessageType.TEXT

        user_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=message_type,
            content=content,
            selected_option=selected_option,
            files=files,
        )

        if selected_option:
            match selected_option.type:
                case OptionType.CLIENT_NAME:
                    system_message = await self._handle_client_name_selection(selected_option, conversation_id)
                case OptionType.LDMF_COUNTRY:
                    system_message = await self._handle_country_selection(selected_option, conversation_id)
                case OptionType.DATES:
                    system_message = await self._handle_dates_selection(selected_option, conversation_id)
                case OptionType.KX_DASH_TASK:
                    system_message = await self.kx_dash_service.on_select(selected_option, conversation_id)
                case _:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
        else:
            if content:
                # Process the user message to get the system reply and intention
                message_processor = ConversationMessageProcessor(
                    conversation_id=conversation_id,
                    user_message=content,
                    files=files,
                    intent_classifier_service=self.intent_classifier_service,
                    extracted_data_service=self.extracted_data_service,
                    conversation_repository=self.conversation_repository,
                    date_validator_service=self.date_validator_service,
                    token=token,
                )
                message_processing_result: ConversationMessageProcessingResult = await message_processor.run()
                system_reply = message_processing_result.system_reply
                intention = message_processing_result.intention
                user_message.intention = intention

                # Generate suggested replies for the user
                suggested_prompts_generator = SuggestedPromptsGenerator(
                    conversation_id=conversation_id,
                    user_message=content,
                    files=files,
                    intention=intention,
                    kx_dash_service=self.kx_dash_service,
                    extracted_data_service=self.extracted_data_service,
                )
                suggested_prompts = await suggested_prompts_generator.run()
            else:
                system_reply = ''
                message_processing_result = ConversationMessageProcessingResult(
                    intention=ConversationMessageIntention.UNDEFINED, system_reply=system_reply, data={}
                )
                suggested_prompts = []

            system_message = await self._get_system_message_with_result(
                user_message, message_processing_result, suggested_prompts
            )

        response = CombinedMessageSerializer(
            user=cast(UserMessageSerializer, await self.create_message(user_message)),
            system=cast(SystemMessageSerializer, await self.create_message(system_message)),
        )

        if user_message.intention == ConversationMessageIntention.EXTRACTION:
            await self.document_service.create_prompt(
                content=user_message.content, signalr_user_id=conversation_id, message_id=response.user.id
            )
        elif user_message.intention == ConversationMessageIntention.DASH_DISCARD:
            await self.extracted_data_service.delete(conversation_id, DataSourceType.KX_DASH)

        if files:
            document_data = DocumentCreationRequest(
                conversation_id=user_message.conversation_id,
                files=files,
                message_id=response.user.id,
            )
            response.files = await self.document_service.create_many(document_data)

        return response

    async def create_message(self, message_data: MessageValidator) -> BaseMessageSerializer:
        """
        Create a new conversation message.

        Args:
            message_data: Data for creating the message

        Returns:
            Response with the created message data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error creating the message
        """
        logger.debug('Creating new message for conversation ID: %s', message_data.conversation_id)
        try:
            return await self.conversation_message_repository.create(message_data)
        except Exception as e:
            logger.error('Error creating message: %s', e)
            raise

    async def get(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get a message by its ID.

        Args:
            public_id: The ID of the message

        Returns:
            The message response

        Raises:
            EntityNotFoundError: If the message doesn't exist
            DatabaseException: If there's an error retrieving the message
        """
        try:
            logger.debug('Retrieving message with ID: %s', public_id)
            return await self.conversation_message_repository.get(public_id)

        except Exception as e:
            logger.error('Error retrieving message: %s', e)
            raise

    async def list(self, public_id: UUID) -> Sequence[BaseMessageSerializer]:
        """
        Get all messages for a specific conversation.

        Args:
            public_id: The ID of the conversation

        Returns:
            Sequence of messages for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the messages
        """
        if not await self.conversation_repository.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        try:
            logger.debug('Retrieving all messages for conversation with ID: %s', public_id)
            return await self.conversation_message_repository.list(public_id)

        except Exception as e:
            logger.error('Error retrieving messages for conversation %s: %s', public_id, e)
            raise

    async def get_last(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get the last message for a specific conversation.

        Args:
            public_id: The ID of the conversation

        Returns:
            The last message for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the last message
        """
        try:
            logger.debug('Retrieving last message for conversation with ID: %s', public_id)
            return await self.conversation_message_repository.get_last(public_id)

        except Exception as e:
            logger.error('Error retrieving last message for conversation %s: %s', public_id, e)
            raise

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all messages for a specific conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            None

        Raises:
            DatabaseException: If there's an error deleting the messages
        """
        try:
            logger.debug('Deleting all messages for conversation with ID: %s', conversation_id)
            await self.conversation_message_repository.delete_many(conversation_id)
        except Exception as e:
            logger.error('Error deleting messages for conversation %s: %s', conversation_id, e)
            raise

    async def _get_system_message_with_result(
        self,
        message_data: MessageValidator,
        processing_result: ConversationMessageProcessingResult,
        suggested_prompts: Sequence[SuggestedUserPrompt],
    ) -> MessageValidator:
        """
        Create a system message based on the message processing result.
        """
        options = []
        # Check if we have options from the processing result
        if 'options' in processing_result.data and processing_result.data['options']:
            # Check conversation state to determine option type
            conversation_state = processing_result.data.get('conversation_state')
            if conversation_state == ConversationState.COLLECTING_CLIENT_NAME:
                # Create ClientNameOption objects
                options = [ClientNameOption(client_name=name) for name in processing_result.data['options']]
            elif conversation_state == ConversationState.COLLECTING_COUNTRY:
                options = [LDMFCountryOption(ldmf_country=name) for name in processing_result.data['options']]
            elif conversation_state == ConversationState.COLLECTING_DATES:
                options = [
                    DatePickerOption(start_date=start_date, end_date=end_date)
                    for start_date, end_date in processing_result.data['options']
                ]
            # Add other option types as needed for other states

        confirmed_data = await self.conversation_repository.get_confirmed_data(public_id=message_data.conversation_id)
        # Fallback to mocked options if no real options available
        if not options:
            content = message_data.content.lower()
            if 'date' in content:
                start_date, end_date = (
                    confirmed_data.date_intervals if confirmed_data.date_intervals else ('2025-5-1', '2025-7-1')
                )
                options = [
                    DatePickerOption(
                        type=OptionType.DATES,
                        start_date=dt.date.fromisoformat(start_date) if start_date else None,
                        end_date=dt.date.fromisoformat(end_date) if end_date else None,
                    )
                ]
            elif 'country' in content:
                ldmf_countries = [confirmed_data.ldmf_country] if confirmed_data.ldmf_country else ['Austria', 'France']
                options = [LDMFCountryOption(ldmf_country=country) for country in ldmf_countries]
            elif 'client' in content:
                client_names = (
                    [confirmed_data.client_name]
                    if confirmed_data.client_name
                    else ['FashionForward', 'FashionForward GMBH']
                )
                options = [ClientNameOption(client_name=name) for name in client_names]

        return MessageValidator(
            conversation_id=message_data.conversation_id,
            role=MessageRole.SYSTEM,
            type=message_data.type,
            content=processing_result.system_reply,
            options=options,
            suggested_prompts=[i.value for i in suggested_prompts],
        )

    async def get_owner_id(self, message_id: UUID) -> UUID | None:
        """
        Get an owner ID for the message.

        Args:
            message_id: The ID of the message

        Returns:
            The user ID if the message exists, None otherwise
        """
        try:
            logger.debug('Retrieving an owner ID for the message: %s', message_id)
            return await self.conversation_message_repository.get_owner_id(message_id)

        except Exception:
            logger.exception('Failed to retrieve message owner ID')
            raise

    async def _handle_country_selection(
        self, selected_option: LDMFCountryOption, conversation_id: UUID
    ) -> MessageValidator:
        try:
            logger.debug(f'Handling selection {selected_option.ldmf_country} for conversation {conversation_id}')

            # Update confirmed data with the selected country
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name='ldmf_country',
                field_value=selected_option.ldmf_country,
                state=ConversationState.COLLECTING_DATES,
            )

            # Create confirmation message
            confirmation_message = LDMF_COUNTRY_CONFIRMED.format(ldmf_country=selected_option.ldmf_country)

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
            )

        except Exception as e:
            logger.error('Error handling country selection: %s', e)
            raise

    async def _handle_dates_selection(
        self, selected_option: DatePickerOption, conversation_id: UUID
    ) -> MessageValidator:
        """
        Handle dates selection from options.

        Args:
            selected_option: The selected dates option
            conversation_id: The conversation ID

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            logger.debug('Handling dates selection: %s for conversation: %s', selected_option, conversation_id)
            start_date = selected_option.start_date.isoformat() if selected_option.start_date else None
            end_date = selected_option.end_date.isoformat() if selected_option.end_date else None

            # Create confirmation message
            confirmation_message = DATES_CONFIRMED
            options = []

            if start_date and end_date:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_OBJECTIVE,  # Move to next state
                )
            else:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_DATES,  # Stay in the same state
                )
                confirmation_message = DATES_ONE_DATE
                options = [selected_option]

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                options=options,
            )

        except Exception as e:
            logger.error('Error handling dates selection: %s', e)
            raise

    async def _handle_client_name_selection(
        self, selected_option: ClientNameOption, conversation_id: UUID
    ) -> MessageValidator:
        """
        Handle client name selection from options.

        Args:
            selected_option: The selected client name option
            conversation_id: The conversation ID

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            logger.debug(
                'Handling client name selection: %s for conversation: %s', selected_option.client_name, conversation_id
            )

            # Update confirmed data with the selected client name
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name='client_name',
                field_value=selected_option.client_name,
                state=ConversationState.COLLECTING_COUNTRY,  # Move to next state
            )

            # Create confirmation message
            confirmation_message = CLIENT_NAME_CONFIRMED.format(client_name=selected_option.client_name)

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
            )

        except Exception as e:
            logger.error('Error handling client name selection: %s', e)
            raise
