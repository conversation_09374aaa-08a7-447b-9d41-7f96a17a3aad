import logging

from httpx import AsyncClient

from config import settings
from core.repositories import BaseHttpRepository
from schemas import CountryData


__all__ = ['LDMFCountriesRepository']

logger = logging.getLogger(__name__)


class LDMFCountriesRepository(BaseHttpRepository):
    """Repository for LDMF Countries API operations."""

    def __init__(self, http_client: AsyncClient):
        """
        Initialize the LDMF Countries Repository with an HTTP client.

        Args:
            http_client: The AsyncClient to use for requests
        """
        super().__init__(http_client)
        self._base_path = settings.ldmf_countries_api.base_url

    async def get_ldmf_countries(self, token: str) -> list[CountryData]:
        """
        Get a list of LDMF countries from the API.

        Args:
            token: Bearer token for authentication

        Returns:
            List of country data with format:
            [
                {'memberFirmId': 2470, 'name': 'Germany', 'id': 158},
                {'memberFirmId': 0, 'name': 'Chad', 'id': 141},
                {'memberFirmId': 2768, 'name': 'United States', 'id': 37}
            ]

        Raises:
            HTTPStatusError: If the API request fails
            TransportError: If there's a network error
        """
        url = self._base_path
        headers = {'Authorization': f'Bearer {token}'}
        response = await self._execute_request('get', url, headers=headers)
        return [CountryData(**country) for country in response]
